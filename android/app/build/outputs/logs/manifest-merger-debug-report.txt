-- Merging decision tree log ---
application
INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:2:5-33:19
INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
MERGED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-17:19
MERGED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:5-17:19
MERGED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-46:19
MERGED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:5-46:19
MERGED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-32:19
MERGED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-32:19
MERGED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-17:19
MERGED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-17:19
MERGED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-12:19
MERGED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-13:19
MERGED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-23:19
MERGED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:5-23:19
MERGED from [com.google.firebase:firebase-analytics:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/de09d56ede5c875ebbc0232053614904/transformed/jetified-firebase-analytics-22.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/de09d56ede5c875ebbc0232053614904/transformed/jetified-firebase-analytics-22.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:29:5-37:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:29:5-37:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/a8fbf22bcb175ef393e26705bdeff75e/transformed/jetified-play-services-measurement-sdk-22.5.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/a8fbf22bcb175ef393e26705bdeff75e/transformed/jetified-play-services-measurement-sdk-22.5.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/19e5c96d7b98bac1a08d2c99339ede92/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/19e5c96d7b98bac1a08d2c99339ede92/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/03c47e1b6cff250dae244f6621ea4a52/transformed/jetified-play-services-measurement-base-22.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/03c47e1b6cff250dae244f6621ea4a52/transformed/jetified-play-services-measurement-base-22.5.0/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:4:5-6:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:22:5-29:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:4:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
	android:name
		INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml
manifest
ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:1:1-45:12
MERGED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:1:1-45:12
INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml:1:1-7:12
INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml:1:1-7:12
MERGED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-19:12
MERGED from [:flutter_image_compress_common] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-10:12
MERGED from [:image_editor_common] /Users/<USER>/.pub-cache/hosted/pub.dev/image_editor_common-1.2.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:motion] /Users/<USER>/.pub-cache/hosted/pub.dev/motion-2.0.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:package_info_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:shared_preferences_android] /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:webview_flutter_android] /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:device_calendar] /Users/<USER>/.pub-cache/hosted/pub.dev/device_calendar-4.3.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-48:12
MERGED from [:local_auth_android] /Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:17:1-29:12
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/0f448994e490bf59f20aa19226509e50/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/4a06d06ec0ce1c73cc8df2ae11f0f21f/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-34:12
MERGED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-19:12
MERGED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-14:12
MERGED from [:connectivity_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-9:12
MERGED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-15:12
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:fluttertoast] /Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:image_gallery_saver] /Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-25:12
MERGED from [:path_provider_android] /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-11.1.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f96942f3d58d17244242eba2c1bcf26c/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/79275990ee9dddfd68bc7c9d7157e0cd/transformed/recyclerview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/32150ec382526e3003bd883391653303/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/972419750b36e9fbf2d0c26a45927d82/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/de09d56ede5c875ebbc0232053614904/transformed/jetified-firebase-analytics-22.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:17:1-39:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/a8fbf22bcb175ef393e26705bdeff75e/transformed/jetified-play-services-measurement-sdk-22.5.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/19e5c96d7b98bac1a08d2c99339ede92/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/03c47e1b6cff250dae244f6621ea4a52/transformed/jetified-play-services-measurement-base-22.5.0/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b90cb9e6e291107f93471260cd339b4/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:15:1-19:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/8.12/transforms/32f4a2813ac7c771e056d42a720055af/transformed/jetified-activity-ktx-1.9.3/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c244a6ce50b3288fe79d3f6ae212397f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/306016bcb4195b3238dbb4d76cafb64c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2bb4cddb87481b6b754c0fdd3839a9b/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/425b3275685a974b685af27ff4ed6b1d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:17:1-21:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/f71e40716bc29995f4cada24da499d83/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:17:1-31:12
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/31eccd218f5b1fd8272959453f411784/transformed/jetified-datastore-core-release/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/7535a935f9e65beb6c79d36312378a64/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/479b3bf32f00901a230d7d79262001b9/transformed/jetified-datastore-release/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/99bb3b712005fd2917858cd813cd885d/transformed/jetified-ads-adservices-java-1.1.0-beta11/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:2:1-5:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/8.12/transforms/93eeca70efd8419049cd49df8af72af1/transformed/jetified-activity-1.9.3/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.12/transforms/6d1284f70efaea5e2f7428ac2d9ae231/transformed/webkit-1.12.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/861e783eb94c5cf895f7fc30dea4170d/transformed/media-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/08d6944c906bcd30c9d42a63993176cf/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/f87704cc6ac259b753f491455f413615/transformed/transition-1.4.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.heifwriter:heifwriter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/00f59a13534ab770a6a6dbae77e73f7c/transformed/heifwriter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.12/transforms/17952863fa1b6f5dddf3dbb6f4ce2941/transformed/exifinterface-1.3.7/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/277474532995b9fd32fdd0e838dc6db6/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:15:1-20:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cb263d6c3807b4994a64a61fe4ea2bcf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/683cbfde6a58705556f8fa87883a18e1/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:2:1-7:12
MERGED from [com.jaredrummler:truetypeparser-light:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4ba5b25382fbfb94889bc2aecedf6510/transformed/jetified-truetypeparser-light-1.0.0/AndroidManifest.xml:18:1-25:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:2:1-7:12
	package
		INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
	android:versionCode
		INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:1:11-69
queries
ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:39:5-44:15
MERGED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-11:15
MERGED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-11:15
intent#action:name:android.intent.action.PROCESS_TEXT+data:mimeType:text/plain
ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:40:9-43:18
action#android.intent.action.PROCESS_TEXT
ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:41:13-72
	android:name
		ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:41:21-70
data
ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:42:13-50
	android:mimeType
		ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:42:19-48
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml:6:5-66
MERGED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-67
MERGED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:22:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:23:5-67
	android:name
		ADDED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml:6:22-64
uses-sdk
INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
MERGED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_image_compress_common] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-8:55
MERGED from [:flutter_image_compress_common] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-8:55
MERGED from [:image_editor_common] /Users/<USER>/.pub-cache/hosted/pub.dev/image_editor_common-1.2.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:image_editor_common] /Users/<USER>/.pub-cache/hosted/pub.dev/image_editor_common-1.2.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:motion] /Users/<USER>/.pub-cache/hosted/pub.dev/motion-2.0.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:motion] /Users/<USER>/.pub-cache/hosted/pub.dev/motion-2.0.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:package_info_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:shared_preferences_android] /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:device_calendar] /Users/<USER>/.pub-cache/hosted/pub.dev/device_calendar-4.3.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:device_calendar] /Users/<USER>/.pub-cache/hosted/pub.dev/device_calendar-4.3.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:local_auth_android] /Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:local_auth_android] /Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/0f448994e490bf59f20aa19226509e50/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/0f448994e490bf59f20aa19226509e50/transformed/jetified-appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/4a06d06ec0ce1c73cc8df2ae11f0f21f/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.12/transforms/4a06d06ec0ce1c73cc8df2ae11f0f21f/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:connectivity_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] /Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:fluttertoast] /Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:image_gallery_saver] /Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:image_gallery_saver] /Users/<USER>/.pub-cache/hosted/pub.dev/image_gallery_saver-2.0.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:6:5-44
MERGED from [:path_provider_android] /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-11.1.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-11.1.0/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f96942f3d58d17244242eba2c1bcf26c/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/f96942f3d58d17244242eba2c1bcf26c/transformed/legacy-support-v4-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/79275990ee9dddfd68bc7c9d7157e0cd/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/79275990ee9dddfd68bc7c9d7157e0cd/transformed/recyclerview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/984bdeb02044daf662dc2d3e1fe07483/transformed/legacy-support-core-ui-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/32150ec382526e3003bd883391653303/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/32150ec382526e3003bd883391653303/transformed/swiperefreshlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/972419750b36e9fbf2d0c26a45927d82/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment-ktx:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/972419750b36e9fbf2d0c26a45927d82/transformed/jetified-fragment-ktx-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/de09d56ede5c875ebbc0232053614904/transformed/jetified-firebase-analytics-22.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/de09d56ede5c875ebbc0232053614904/transformed/jetified-firebase-analytics-22.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/a8fbf22bcb175ef393e26705bdeff75e/transformed/jetified-play-services-measurement-sdk-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/a8fbf22bcb175ef393e26705bdeff75e/transformed/jetified-play-services-measurement-sdk-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/19e5c96d7b98bac1a08d2c99339ede92/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/19e5c96d7b98bac1a08d2c99339ede92/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/03c47e1b6cff250dae244f6621ea4a52/transformed/jetified-play-services-measurement-base-22.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/03c47e1b6cff250dae244f6621ea4a52/transformed/jetified-play-services-measurement-base-22.5.0/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] /Users/<USER>/.gradle/caches/8.12/transforms/500a0c9cb71e092f235465c1951cf452/transformed/jetified-play-services-stats-17.0.2/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b90cb9e6e291107f93471260cd339b4/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/2b90cb9e6e291107f93471260cd339b4/transformed/jetified-firebase-installations-interop-17.1.1/AndroidManifest.xml:17:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d21df4d1a80ec9bf2502ed8e05d37297/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/df95e149204d983cb4c11efbc84ab4c1/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/8.12/transforms/32f4a2813ac7c771e056d42a720055af/transformed/jetified-activity-ktx-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.9.3] /Users/<USER>/.gradle/caches/8.12/transforms/32f4a2813ac7c771e056d42a720055af/transformed/jetified-activity-ktx-1.9.3/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c244a6ce50b3288fe79d3f6ae212397f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/c244a6ce50b3288fe79d3f6ae212397f/transformed/jetified-lifecycle-runtime-ktx-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/e72f610bb8a20735f78a04c908b9b793/transformed/lifecycle-livedata-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/306016bcb4195b3238dbb4d76cafb64c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/306016bcb4195b3238dbb4d76cafb64c/transformed/jetified-lifecycle-viewmodel-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc0590902d0fbba9efca7bc74a8bc4cb/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/23f1459f3a17c3f297faa9e854d895db/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/1e1e86d9fc1ea8180a98a95859125403/transformed/lifecycle-livedata-core-2.7.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2bb4cddb87481b6b754c0fdd3839a9b/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/c2bb4cddb87481b6b754c0fdd3839a9b/transformed/jetified-emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/425b3275685a974b685af27ff4ed6b1d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/425b3275685a974b685af27ff4ed6b1d/transformed/jetified-savedstate-ktx-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/51d67b28f04358995f888f3317b40779/transformed/jetified-window-java-1.2.0/AndroidManifest.xml:19:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/f71e40716bc29995f4cada24da499d83/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/f71e40716bc29995f4cada24da499d83/transformed/slidingpanelayout-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/31eccd218f5b1fd8272959453f411784/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-core-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/31eccd218f5b1fd8272959453f411784/transformed/jetified-datastore-core-release/AndroidManifest.xml:5:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/7535a935f9e65beb6c79d36312378a64/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-preferences-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/7535a935f9e65beb6c79d36312378a64/transformed/jetified-datastore-preferences-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/479b3bf32f00901a230d7d79262001b9/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.datastore:datastore-android:1.1.3] /Users/<USER>/.gradle/caches/8.12/transforms/479b3bf32f00901a230d7d79262001b9/transformed/jetified-datastore-release/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/99bb3b712005fd2917858cd813cd885d/transformed/jetified-ads-adservices-java-1.1.0-beta11/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/99bb3b712005fd2917858cd813cd885d/transformed/jetified-ads-adservices-java-1.1.0-beta11/AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/7795f05fbf8283409257f8d4d50c7a58/transformed/jetified-play-services-tasks-18.2.0/AndroidManifest.xml:3:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/c1410397b3c38d45fcfb6f835e622ed9/transformed/jetified-firebase-measurement-connector-19.0.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] /Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/8.12/transforms/93eeca70efd8419049cd49df8af72af1/transformed/jetified-activity-1.9.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.9.3] /Users/<USER>/.gradle/caches/8.12/transforms/93eeca70efd8419049cd49df8af72af1/transformed/jetified-activity-1.9.3/AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.12/transforms/6d1284f70efaea5e2f7428ac2d9ae231/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] /Users/<USER>/.gradle/caches/8.12/transforms/6d1284f70efaea5e2f7428ac2d9ae231/transformed/webkit-1.12.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/10acfa95459151f0abcb0437238b9ca7/transformed/drawerlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/e7b4e62af0008ea11d5619489212cc48/transformed/jetified-core-ktx-1.13.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/861e783eb94c5cf895f7fc30dea4170d/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/861e783eb94c5cf895f7fc30dea4170d/transformed/media-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/d2f89760d0d7afc020c36efe677962c0/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/afec9dc0bcc11d087323dc11f5e0350a/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/ee817ed912dd87a9ffe7b0d8087b9e11/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/106b34a8e64882148068274b889c0b9f/transformed/asynclayoutinflater-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/08d6944c906bcd30c9d42a63993176cf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/08d6944c906bcd30c9d42a63993176cf/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/f87704cc6ac259b753f491455f413615/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/f87704cc6ac259b753f491455f413615/transformed/transition-1.4.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.heifwriter:heifwriter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/00f59a13534ab770a6a6dbae77e73f7c/transformed/heifwriter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.heifwriter:heifwriter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/00f59a13534ab770a6a6dbae77e73f7c/transformed/heifwriter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.12/transforms/17952863fa1b6f5dddf3dbb6f4ce2941/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.7] /Users/<USER>/.gradle/caches/8.12/transforms/17952863fa1b6f5dddf3dbb6f4ce2941/transformed/exifinterface-1.3.7/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/27003765dae66b7dc3bf878451ba1684/transformed/jetified-tracing-1.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4cfa7aabd0ff8beb21daa4d12f46b519/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/f3b8632830106ae874bd20aa567485d8/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/dc56adcbf17b642cc8bc810bfcbda96d/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/1d33f966f1aab687e952d4b7cce6845e/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/277474532995b9fd32fdd0e838dc6db6/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/277474532995b9fd32fdd0e838dc6db6/transformed/jetified-firebase-components-18.0.0/AndroidManifest.xml:18:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a58c138301656e62a00a9163f21e3a54/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cb263d6c3807b4994a64a61fe4ea2bcf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/cb263d6c3807b4994a64a61fe4ea2bcf/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/b7f63da0fad92ba134922d35b82d48c3/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/449958d8d573c37840f9e10ca78b3740/transformed/jetified-core-1.0.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/683cbfde6a58705556f8fa87883a18e1/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] /Users/<USER>/.gradle/caches/8.12/transforms/683cbfde6a58705556f8fa87883a18e1/transformed/jetified-annotation-experimental-1.4.1/AndroidManifest.xml:5:5-44
MERGED from [com.jaredrummler:truetypeparser-light:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4ba5b25382fbfb94889bc2aecedf6510/transformed/jetified-truetypeparser-light-1.0.0/AndroidManifest.xml:21:5-23:41
MERGED from [com.jaredrummler:truetypeparser-light:1.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/4ba5b25382fbfb94889bc2aecedf6510/transformed/jetified-truetypeparser-light-1.0.0/AndroidManifest.xml:21:5-23:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] /Users/<USER>/.gradle/caches/8.12/transforms/aa2b156f95f9eab66ccb02ea9eacfedd/transformed/jetified-relinker-1.4.5/AndroidManifest.xml:5:5-43
	tools:overrideLibrary
		ADDED from [:flutter_image_compress_common] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_image_compress_common-1.0.6/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-52
	android:targetSdkVersion
		INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [:connectivity_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-4.0.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:24:5-79
	android:name
		ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-76
uses-permission#android.permission.WAKE_LOCK
ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:25:5-68
	android:name
		ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:22-65
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-16:19
MERGED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-12:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:30:9-36:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:30:9-36:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:32:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar
ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-15:85
	android:value
		ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-82
	android:name
		ADDED from [:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:17-128
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-10:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-90
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-87
activity#com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-18:47
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-37
	android:configChanges
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-137
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-44
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-112
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-22:55
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-120
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:9-26:55
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-114
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:9-31:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-134
activity#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:32:9-36:55
	android:launchMode
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:13-48
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:13-37
	android:theme
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-52
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:33:13-128
receiver#com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:9-41:40
	android:enabled
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:40:13-35
	android:exported
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:41:13-37
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:39:13-119
meta-data#io.flutter.embedded_views_preview
ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:9-45:36
	android:value
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:45:13-33
	android:name
		ADDED from [:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-61
uses-permission#android.permission.USE_BIOMETRIC
ADDED from [:local_auth_android] /Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-72
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:24:5-72
MERGED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:24:5-72
	android:name
		ADDED from [:local_auth_android] /Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-69
uses-permission#android.permission.USE_FINGERPRINT
ADDED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:27:5-74
	android:name
		ADDED from [androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:27:22-71
provider#io.flutter.plugins.imagepicker.ImagePickerFileProvider
ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-17:20
	android:grantUriPermissions
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
	android:authorities
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-74
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-82
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
	android:resource
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
service#com.google.android.gms.metadata.ModuleDependencies
ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:19
	android:enabled
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-36
	android:exported
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
	tools:ignore
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:13-40
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-78
intent-filter#action:name:com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-26:29
action#com.google.android.gms.metadata.MODULE_DEPENDENCIES
ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-94
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-91
meta-data#photopicker_activity:0:required
ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-30:36
	android:value
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-33
	android:name
		ADDED from [:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-63
provider#io.flutter.plugins.share.ShareFileProvider
ADDED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-74
	android:exported
		ADDED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-70
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-10:38
	android:maxSdkVersion
		ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-35
	android:name
		ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-64
provider#com.joutvhu.openfile.FileProvider
ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-22:20
	android:grantUriPermissions
		ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
	android:authorities
		ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-85
	android:exported
		ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
	tools:replace
		ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-48
	android:name
		ADDED from [:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-61
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/19e5c96d7b98bac1a08d2c99339ede92/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/19e5c96d7b98bac1a08d2c99339ede92/transformed/jetified-play-services-ads-identifier-18.0.0/AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/70bc8784d05e62c347fa6d2289a79f9d/transformed/jetified-play-services-measurement-sdk-api-22.5.0/AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:27:22-79
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:34:17-139
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/c534b28ad3ba39c2f9b5b46c50d64cb9/transformed/jetified-play-services-measurement-impl-22.5.0/AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:40:13-87
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:24:13-50
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
permission#com.example.sbar_pos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
uses-permission#com.example.sbar_pos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
