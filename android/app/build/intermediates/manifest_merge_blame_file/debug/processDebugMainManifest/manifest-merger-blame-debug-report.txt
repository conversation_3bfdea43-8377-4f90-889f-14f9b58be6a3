1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.sbar_pos"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml:6:5-66
15-->/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/debug/AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:39:5-44:15
24        <intent>
24-->/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:41:13-72
25-->/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:42:13-50
27-->/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/AndroidManifest.xml:42:19-48
28        </intent>
29        <intent>
29-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-10:18
30            <action android:name="android.support.customtabs.action.CustomTabsService" />
30-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-90
30-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:21-87
31        </intent>
32    </queries>
33
34    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
34-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-79
34-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-76
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-68
35-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:22-65
36    <uses-permission android:name="android.permission.USE_BIOMETRIC" /> <!-- suppress DeprecatedClassUsageInspection -->
36-->[:local_auth_android] /Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-72
36-->[:local_auth_android] /Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:22-69
37    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
37-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:27:5-74
37-->[androidx.biometric:biometric:1.1.0] /Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/AndroidManifest.xml:27:22-71
38    <uses-permission
38-->[:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:5-10:38
39        android:name="android.permission.READ_EXTERNAL_STORAGE"
39-->[:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-64
40        android:maxSdkVersion="32" />
40-->[:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-35
41    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
41-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:25:5-79
41-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:25:22-76
42    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
42-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:26:5-88
42-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:26:22-85
43    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
43-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:27:5-82
43-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:27:22-79
44    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
44-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:26:5-110
44-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:26:22-107
45
46    <permission
46-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:22:5-24:47
47        android:name="com.example.sbar_pos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.example.sbar_pos.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:26:22-94
51
52    <application
53        android:name="android.app.Application"
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.13.1] /Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/AndroidManifest.xml:28:18-86
55        android:debuggable="true"
56        android:extractNativeLibs="true"
57        android:icon="@mipmap/ic_launcher"
58        android:label="sbar_pos" >
59        <activity
60            android:name="com.example.sbar_pos.MainActivity"
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
62            android:exported="true"
63            android:hardwareAccelerated="true"
64            android:launchMode="singleTop"
65            android:taskAffinity=""
66            android:theme="@style/LaunchTheme"
67            android:windowSoftInputMode="adjustResize" >
68
69            <!--
70                 Specifies an Android theme to apply to this Activity as soon as
71                 the Android process has started. This theme is visible to the user
72                 while the Flutter UI initializes. After that, this theme continues
73                 to determine the Window background behind the Flutter UI.
74            -->
75            <meta-data
76                android:name="io.flutter.embedding.android.NormalTheme"
77                android:resource="@style/NormalTheme" />
78
79            <intent-filter>
80                <action android:name="android.intent.action.MAIN" />
81
82                <category android:name="android.intent.category.LAUNCHER" />
83            </intent-filter>
84        </activity>
85        <!--
86             Don't delete the meta-data below.
87             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
88        -->
89        <meta-data
90            android:name="flutterEmbedding"
91            android:value="2" />
92
93        <service
93-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:9-16:19
94            android:name="com.google.firebase.components.ComponentDiscoveryService"
94-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:18-89
95            android:directBootAware="true"
95-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:32:13-43
96            android:exported="false" >
96-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:32:13-37
97            <meta-data
97-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-15:85
98                android:name="com.google.firebase.components:io.flutter.plugins.firebase.analytics.FlutterFirebaseAppRegistrar"
98-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:17-128
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[:firebase_analytics] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.2/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-82
100            <meta-data
100-->[:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-11:85
101                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
101-->[:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:17-124
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[:firebase_core] /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:17-82
103            <meta-data
103-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:33:13-35:85
104                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
104-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:34:17-139
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.android.gms:play-services-measurement-api:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/43080682432ccc0c31fbec6f5e1ca14a/transformed/jetified-play-services-measurement-api-22.5.0/AndroidManifest.xml:35:17-82
106            <meta-data
106-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:15:13-17:85
107                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
107-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:16:17-130
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:17:17-82
109            <meta-data
109-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:18:13-20:85
110                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
110-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:19:17-127
111                android:value="com.google.firebase.components.ComponentRegistrar" />
111-->[com.google.firebase:firebase-installations:18.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/e36da2f60198548da2dac483ab601381/transformed/jetified-firebase-installations-18.0.0/AndroidManifest.xml:20:17-82
112            <meta-data
112-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:12:13-14:85
113                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
113-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:13:17-116
114                android:value="com.google.firebase.components.ComponentRegistrar" />
114-->[com.google.firebase:firebase-common-ktx:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/a061838a5592a55f9a915626b6a31f6d/transformed/jetified-firebase-common-ktx-21.0.0/AndroidManifest.xml:14:17-82
115            <meta-data
115-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:35:13-37:85
116                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
116-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:36:17-109
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:37:17-82
118        </service>
119
120        <activity
120-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:9-18:47
121            android:name="com.pichillilorenzo.flutter_inappwebview_android.in_app_browser.InAppBrowserActivity"
121-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-112
122            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density"
122-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-137
123            android:exported="false"
123-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-37
124            android:theme="@style/AppTheme" />
124-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:18:13-44
125        <activity
125-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-22:55
126            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivity"
126-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-120
127            android:exported="false"
127-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-37
128            android:theme="@style/ThemeTransparent" />
128-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-52
129        <activity
129-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:23:9-26:55
130            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivity"
130-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-114
131            android:exported="false"
131-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:13-37
132            android:theme="@style/ThemeTransparent" />
132-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:26:13-52
133        <activity
133-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:27:9-31:55
134            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ChromeCustomTabsActivitySingleInstance"
134-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-134
135            android:exported="false"
135-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:13-37
136            android:launchMode="singleInstance"
136-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:13-48
137            android:theme="@style/ThemeTransparent" />
137-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:31:13-52
138        <activity
138-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:32:9-36:55
139            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.TrustedWebActivitySingleInstance"
139-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:33:13-128
140            android:exported="false"
140-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:34:13-37
141            android:launchMode="singleInstance"
141-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:35:13-48
142            android:theme="@style/ThemeTransparent" />
142-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:36:13-52
143
144        <receiver
144-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:38:9-41:40
145            android:name="com.pichillilorenzo.flutter_inappwebview_android.chrome_custom_tabs.ActionBroadcastReceiver"
145-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:39:13-119
146            android:enabled="true"
146-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:40:13-35
147            android:exported="false" />
147-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:41:13-37
148
149        <meta-data
149-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:43:9-45:36
150            android:name="io.flutter.embedded_views_preview"
150-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:44:13-61
151            android:value="true" />
151-->[:flutter_inappwebview_android] /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:45:13-33
152
153        <provider
153-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:9-17:20
154            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
154-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-82
155            android:authorities="com.example.sbar_pos.flutter.image_provider"
155-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-74
156            android:exported="false"
156-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-37
157            android:grantUriPermissions="true" >
157-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-47
158            <meta-data
158-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
159                android:name="android.support.FILE_PROVIDER_PATHS"
159-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
160                android:resource="@xml/flutter_image_picker_file_paths" />
160-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
161        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
162        <service
162-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:19:9-31:19
163            android:name="com.google.android.gms.metadata.ModuleDependencies"
163-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:20:13-78
164            android:enabled="false"
164-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:21:13-36
165            android:exported="false" >
165-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:22:13-37
166            <intent-filter>
166-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:24:13-26:29
167                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
167-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:17-94
167-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:25:25-91
168            </intent-filter>
169
170            <meta-data
170-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:28:13-30:36
171                android:name="photopicker_activity:0:required"
171-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:29:17-63
172                android:value="" />
172-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:30:17-33
173        </service>
174
175        <provider
175-->[:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-16:20
176            android:name="io.flutter.plugins.share.ShareFileProvider"
176-->[:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-70
177            android:authorities="com.example.sbar_pos.flutter.share_provider"
177-->[:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-74
178            android:exported="false"
178-->[:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-37
179            android:grantUriPermissions="true" >
179-->[:share] /Users/<USER>/.pub-cache/hosted/pub.dev/share-2.0.4/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-47
180            <meta-data
180-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
181                android:name="android.support.FILE_PROVIDER_PATHS"
181-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
182                android:resource="@xml/flutter_share_file_paths" />
182-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
183        </provider>
184
185        <activity
185-->[:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:8:9-11:74
186            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
186-->[:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:13-74
187            android:exported="false"
187-->[:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:13-37
188            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
188-->[:url_launcher_android] /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-71
189
190        <provider
190-->[:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:9-22:20
191            android:name="com.joutvhu.openfile.FileProvider"
191-->[:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-61
192            android:authorities="com.example.sbar_pos.fileProvider.com.joutvhu.openfile"
192-->[:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:13-85
193            android:exported="false"
193-->[:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:13-37
194            android:grantUriPermissions="true" >
194-->[:open_file_plus] /Users/<USER>/.pub-cache/hosted/pub.dev/open_file_plus-3.4.1+1/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:17:13-47
195            <meta-data
195-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-16:75
196                android:name="android.support.FILE_PROVIDER_PATHS"
196-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:15:17-67
197                android:resource="@xml/filepaths" />
197-->[:image_picker_android] /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/android/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:16:17-72
198        </provider>
199        <provider
199-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:23:9-28:39
200            android:name="com.google.firebase.provider.FirebaseInitProvider"
200-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:24:13-77
201            android:authorities="com.example.sbar_pos.firebaseinitprovider"
201-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:25:13-72
202            android:directBootAware="true"
202-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:26:13-43
203            android:exported="false"
203-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:27:13-37
204            android:initOrder="100" />
204-->[com.google.firebase:firebase-common:21.0.0] /Users/<USER>/.gradle/caches/8.12/transforms/2da272753831444f8032e334cfd31d8b/transformed/jetified-firebase-common-21.0.0/AndroidManifest.xml:28:13-36
205
206        <receiver
206-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:29:9-33:20
207            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
207-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:30:13-85
208            android:enabled="true"
208-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:31:13-35
209            android:exported="false" >
209-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:32:13-37
210        </receiver>
211
212        <service
212-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:35:9-38:40
213            android:name="com.google.android.gms.measurement.AppMeasurementService"
213-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:36:13-84
214            android:enabled="true"
214-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:37:13-35
215            android:exported="false" />
215-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:38:13-37
216        <service
216-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:39:9-43:72
217            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
217-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:40:13-87
218            android:enabled="true"
218-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:41:13-35
219            android:exported="false"
219-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:42:13-37
220            android:permission="android.permission.BIND_JOB_SERVICE" />
220-->[com.google.android.gms:play-services-measurement:22.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/8233ea89672697f3876f24affa9cbe08/transformed/jetified-play-services-measurement-22.5.0/AndroidManifest.xml:43:13-69
221
222        <activity
222-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:9-173
223            android:name="com.google.android.gms.common.api.GoogleApiActivity"
223-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:19-85
224            android:exported="false"
224-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:146-170
225            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
225-->[com.google.android.gms:play-services-base:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/AndroidManifest.xml:5:86-145
226
227        <provider
227-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
228            android:name="androidx.startup.InitializationProvider"
228-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:25:13-67
229            android:authorities="com.example.sbar_pos.androidx-startup"
229-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:26:13-68
230            android:exported="false" >
230-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:27:13-37
231            <meta-data
231-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
232                android:name="androidx.emoji2.text.EmojiCompatInitializer"
232-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:30:17-75
233                android:value="androidx.startup" />
233-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/2f9e30b92e09a4c79f34f925067bc407/transformed/jetified-emoji2-1.2.0/AndroidManifest.xml:31:17-49
234            <meta-data
234-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:29:13-31:52
235                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
235-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:30:17-78
236                android:value="androidx.startup" />
236-->[androidx.lifecycle:lifecycle-process:2.7.0] /Users/<USER>/.gradle/caches/8.12/transforms/0c7cd1674da718ccee593f79cf8da244/transformed/jetified-lifecycle-process-2.7.0/AndroidManifest.xml:31:17-49
237            <meta-data
237-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:29:13-31:52
238                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
238-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:30:17-85
239                android:value="androidx.startup" />
239-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:31:17-49
240        </provider>
241
242        <uses-library
242-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:23:9-25:40
243            android:name="androidx.window.extensions"
243-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:24:13-54
244            android:required="false" />
244-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:25:13-37
245        <uses-library
245-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:26:9-28:40
246            android:name="androidx.window.sidecar"
246-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:27:13-51
247            android:required="false" />
247-->[androidx.window:window:1.2.0] /Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/AndroidManifest.xml:28:13-37
248        <uses-library
248-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:23:9-25:40
249            android:name="android.ext.adservices"
249-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:24:13-50
250            android:required="false" />
250-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] /Users/<USER>/.gradle/caches/8.12/transforms/a1a24f4bb2c37cae8017775dcf0c1a7d/transformed/jetified-ads-adservices-1.1.0-beta11/AndroidManifest.xml:25:13-37
251
252        <meta-data
252-->[com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:21:9-23:69
253            android:name="com.google.android.gms.version"
253-->[com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:22:13-58
254            android:value="@integer/google_play_services_version" />
254-->[com.google.android.gms:play-services-basement:18.5.0] /Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/AndroidManifest.xml:23:13-66
255
256        <receiver
256-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:34:9-52:20
257            android:name="androidx.profileinstaller.ProfileInstallReceiver"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:35:13-76
258            android:directBootAware="false"
258-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:36:13-44
259            android:enabled="true"
259-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:37:13-35
260            android:exported="true"
260-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:38:13-36
261            android:permission="android.permission.DUMP" >
261-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:39:13-57
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:40:13-42:29
263                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
263-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:17-91
263-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:41:25-88
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:43:13-45:29
266                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
266-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:17-85
266-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:44:25-82
267            </intent-filter>
268            <intent-filter>
268-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:46:13-48:29
269                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
269-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:17-88
269-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:47:25-85
270            </intent-filter>
271            <intent-filter>
271-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:49:13-51:29
272                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
272-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:17-95
272-->[androidx.profileinstaller:profileinstaller:1.3.1] /Users/<USER>/.gradle/caches/8.12/transforms/a41c77be64ae79775eea5abf0296f1bb/transformed/jetified-profileinstaller-1.3.1/AndroidManifest.xml:50:25-92
273            </intent-filter>
274        </receiver>
275    </application>
276
277</manifest>
