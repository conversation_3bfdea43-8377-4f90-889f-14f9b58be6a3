{"logs": [{"outputFile": "com.example.sbar_pos.app-mergeDebugResources-71:/values-gu/values-gu.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2788,2882,2985,3082,3184,3286,3384,8050", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "2877,2980,3077,3179,3281,3379,3501,8146"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,177,263,344,486,655,739", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "172,258,339,481,650,734,816"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5795,6054,7746,7827,8151,8320,8404", "endColumns": "71,85,80,141,168,83,81", "endOffsets": "5862,6135,7822,7964,8315,8399,8481"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,100", "endOffsets": "151,252,361,462"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5867,6140,6241,6350", "endColumns": "100,100,108,100", "endOffsets": "5963,6236,6345,6446"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/res/values-gu/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4488", "endColumns": "146", "endOffsets": "4630"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4a06d06ec0ce1c73cc8df2ae11f0f21f/transformed/appcompat-1.6.1/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,7969", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,8045"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/res/values-gu/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3506,3614,3780,3905,4017,4155,4277,4388,4635,4783,4891,5055,5180,5323,5473,5534,5600", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "3609,3775,3900,4012,4150,4272,4383,4483,4778,4886,5050,5175,5318,5468,5529,5595,5677"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/res/values-gu/values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,254,379,506,637,780,915,1057,1154,1291,1429", "endColumns": "112,85,124,126,130,142,134,141,96,136,137,119", "endOffsets": "163,249,374,501,632,775,910,1052,1149,1286,1424,1544"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5682,5968,6451,6576,6703,6834,6977,7112,7254,7351,7488,7626", "endColumns": "112,85,124,126,130,142,134,141,96,136,137,119", "endOffsets": "5790,6049,6571,6698,6829,6972,7107,7249,7346,7483,7621,7741"}}]}]}