{"logs": [{"outputFile": "com.example.sbar_pos.app-mergeDebugResources-71:/values-mn/values-mn.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/res/values-mn/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4540", "endColumns": "149", "endOffsets": "4685"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/res/values-mn/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3532,3642,3799,3931,4038,4175,4301,4430,4690,4834,4941,5109,5238,5379,5547,5608,5670", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "3637,3794,3926,4033,4170,4296,4425,4535,4829,4936,5104,5233,5374,5542,5603,5665,5742"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "5926,6222,6326,6431", "endColumns": "104,103,104,107", "endOffsets": "6026,6321,6426,6534"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,75", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2797,2895,2997,3098,3196,3301,3413,8126", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "2890,2992,3093,3191,3296,3408,3527,8222"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,265,345,483,652,737", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "170,260,340,478,647,732,814"}, "to": {"startLines": "55,58,72,73,76,77,78", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5856,6132,7827,7907,8227,8396,8481", "endColumns": "69,89,79,137,168,84,81", "endOffsets": "5921,6217,7902,8040,8391,8476,8558"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4a06d06ec0ce1c73cc8df2ae11f0f21f/transformed/appcompat-1.6.1/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,2797", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,2873"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,428,514,620,734,817,898,989,1082,1177,1273,1370,1463,1557,1649,1740,1830,1910,2017,2120,2217,2324,2426,2539,2698,8045", "endColumns": "113,99,108,85,105,113,82,80,90,92,94,95,96,92,93,91,90,89,79,106,102,96,106,101,112,158,98,80", "endOffsets": "214,314,423,509,615,729,812,893,984,1077,1172,1268,1365,1458,1552,1644,1735,1825,1905,2012,2115,2212,2319,2421,2534,2693,2792,8121"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/res/values-mn/values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,265,386,521,659,795,923,1073,1167,1301,1437", "endColumns": "108,100,120,134,137,135,127,149,93,133,135,115", "endOffsets": "159,260,381,516,654,790,918,1068,1162,1296,1432,1548"}, "to": {"startLines": "54,57,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5747,6031,6539,6660,6795,6933,7069,7197,7347,7441,7575,7711", "endColumns": "108,100,120,134,137,135,127,149,93,133,135,115", "endOffsets": "5851,6127,6655,6790,6928,7064,7192,7342,7436,7570,7706,7822"}}]}]}