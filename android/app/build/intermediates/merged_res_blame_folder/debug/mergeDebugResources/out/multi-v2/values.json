{"logs": [{"outputFile": "com.example.sbar_pos.app-mergeDebugResources-71:/values/values.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4a06d06ec0ce1c73cc8df2ae11f0f21f/transformed/appcompat-1.6.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1785,1788,1791,1794,1808,1814,1824,1827,1856,1883,1892,1956,2319,2323,2351,2379,2397,2421,2427,2433,2454,2578,2598,2604,2608,2614,2649,2661,2727,2747,2802,2814,2840", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29864,29964,30216,30640,30895,30989,31078,31315,33339,33581,33683,33936,36092,46773,48289,58984,60512,62269,62895,63315,64576,65841,66097,66333,66880,67374,67979,68177,68757,70125,70500,70618,71156,71313,71509,71782,72038,72208,72349,72413,72778,73145,73821,74085,74423,74776,74870,75056,75362,75624,75749,75876,76115,76326,76445,76638,76815,77270,77451,77573,77832,77945,78132,78234,78341,78470,78745,79253,79749,80626,80920,81490,81639,82371,82543,82627,82963,83055,83333,88564,93935,93997,94575,95159,95250,95363,95592,95752,95904,96075,96241,96410,96577,96740,96983,97153,97326,97497,97771,97970,98175,98505,98589,98685,98781,98879,98979,99081,99183,99285,99387,99489,99589,99685,99797,99926,100049,100180,100311,100409,100523,100617,100757,100891,100987,101099,101199,101315,101411,101523,101623,101763,101899,102063,102193,102351,102501,102642,102786,102921,103033,103183,103311,103439,103575,103707,103837,103967,104079,104219,104365,104509,104647,104713,104803,104879,104983,105073,105175,105283,105391,105491,105571,105663,105761,105871,105923,106001,106107,106199,106303,106413,106535,106698,106855,106935,107035,107125,107235,107325,107566,107660,107766,107858,107958,108070,108184,108300,108416,108510,108624,108736,108838,108958,109080,109162,109266,109386,109512,109610,109704,109792,109904,110020,110142,110254,110429,110545,110631,110723,110835,110959,111026,111152,111220,111348,111492,111620,111689,111784,111899,112012,112111,112220,112331,112442,112543,112648,112748,112878,112969,113092,113186,113298,113384,113488,113584,113672,113790,113894,113998,114124,114212,114320,114420,114510,114620,114704,114806,114890,114944,115008,115114,115200,115310,115394,115514,118130,118248,118363,118443,118804,119037,119554,119632,120976,122337,122725,125568,135621,135756,137126,138483,139055,139806,140068,140268,140647,144925,145531,145760,145911,146126,147209,147521,150547,151291,153422,153762,155073", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,904,930,961,969,975,991,1013,1018,1023,1033,1042,1051,1055,1062,1081,1088,1089,1098,1101,1104,1108,1112,1116,1119,1120,1125,1130,1140,1145,1152,1158,1159,1162,1166,1171,1173,1175,1178,1181,1183,1187,1190,1197,1200,1203,1207,1209,1213,1215,1217,1219,1223,1231,1239,1251,1257,1266,1269,1280,1283,1284,1289,1290,1295,1364,1434,1435,1445,1454,1455,1457,1461,1464,1467,1470,1473,1476,1479,1482,1486,1489,1492,1495,1499,1502,1506,1510,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1532,1534,1535,1536,1537,1538,1539,1540,1541,1543,1544,1546,1547,1549,1551,1552,1554,1555,1556,1557,1558,1559,1561,1562,1563,1564,1565,1566,1568,1570,1572,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1586,1587,1588,1589,1590,1591,1592,1594,1598,1602,1603,1604,1605,1606,1607,1611,1612,1613,1614,1616,1618,1620,1622,1624,1625,1626,1627,1629,1631,1633,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1647,1648,1649,1650,1652,1654,1655,1657,1658,1660,1662,1664,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1677,1678,1679,1680,1682,1683,1684,1685,1686,1688,1690,1692,1694,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1784,1787,1790,1793,1807,1813,1823,1826,1855,1882,1891,1955,2318,2322,2350,2378,2396,2420,2426,2432,2453,2577,2597,2603,2607,2613,2648,2660,2726,2746,2801,2813,2839,2846", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29859,29959,30211,30635,30890,30984,31073,31310,33334,33576,33678,33931,36087,46768,48284,58979,60507,62264,62890,63310,64571,65836,66092,66328,66875,67369,67974,68172,68752,70120,70495,70613,71151,71308,71504,71777,72033,72203,72344,72408,72773,73140,73816,74080,74418,74771,74865,75051,75357,75619,75744,75871,76110,76321,76440,76633,76810,77265,77446,77568,77827,77940,78127,78229,78336,78465,78740,79248,79744,80621,80915,81485,81634,82366,82538,82622,82958,83050,83328,88559,93930,93992,94570,95154,95245,95358,95587,95747,95899,96070,96236,96405,96572,96735,96978,97148,97321,97492,97766,97965,98170,98500,98584,98680,98776,98874,98974,99076,99178,99280,99382,99484,99584,99680,99792,99921,100044,100175,100306,100404,100518,100612,100752,100886,100982,101094,101194,101310,101406,101518,101618,101758,101894,102058,102188,102346,102496,102637,102781,102916,103028,103178,103306,103434,103570,103702,103832,103962,104074,104214,104360,104504,104642,104708,104798,104874,104978,105068,105170,105278,105386,105486,105566,105658,105756,105866,105918,105996,106102,106194,106298,106408,106530,106693,106850,106930,107030,107120,107230,107320,107561,107655,107761,107853,107953,108065,108179,108295,108411,108505,108619,108731,108833,108953,109075,109157,109261,109381,109507,109605,109699,109787,109899,110015,110137,110249,110424,110540,110626,110718,110830,110954,111021,111147,111215,111343,111487,111615,111684,111779,111894,112007,112106,112215,112326,112437,112538,112643,112743,112873,112964,113087,113181,113293,113379,113483,113579,113667,113785,113889,113993,114119,114207,114315,114415,114505,114615,114699,114801,114885,114939,115003,115109,115195,115305,115389,115509,118125,118243,118358,118438,118799,119032,119549,119627,120971,122332,122720,125563,135616,135751,137121,138478,139050,139801,140063,140263,140642,144920,145526,145755,145906,146121,147204,147516,150542,151286,153417,153757,155068,155271"}, "to": {"startLines": "36,59,60,91,92,93,95,96,97,98,99,100,101,104,105,106,107,110,111,112,113,114,115,120,121,132,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,149,150,151,152,153,154,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,269,270,275,276,277,278,279,280,281,313,314,315,316,317,318,319,320,356,357,358,359,364,372,373,378,400,406,407,408,409,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,495,500,501,508,509,510,514,522,523,527,531,535,540,546,553,557,561,566,570,574,578,582,586,590,596,600,606,610,616,620,625,629,632,636,642,646,652,656,662,665,669,673,677,681,685,686,687,688,691,694,697,700,704,705,706,707,708,711,713,715,717,722,723,727,733,737,738,740,752,753,757,763,767,768,769,773,800,804,805,809,837,1009,1035,1206,1232,1263,1271,1277,1293,1315,1320,1325,1335,1344,1353,1357,1364,1383,1390,1391,1400,1403,1406,1410,1414,1418,1421,1422,1427,1432,1442,1447,1454,1460,1461,1464,1468,1473,1475,1477,1480,1483,1485,1489,1492,1499,1502,1505,1509,1511,1515,1517,1519,1521,1525,1533,1541,1553,1559,1568,1571,1582,1585,1586,1591,1592,1624,1693,1763,1764,1774,1783,1935,1937,1941,1944,1947,1950,1953,1956,1959,1962,1966,1969,1972,1975,1979,1982,1986,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2012,2014,2015,2016,2017,2018,2019,2020,2021,2023,2024,2026,2027,2029,2031,2032,2034,2035,2036,2037,2038,2039,2041,2042,2043,2044,2045,2062,2064,2066,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2080,2082,2083,2084,2085,2086,2087,2088,2090,2094,2098,2099,2100,2101,2102,2103,2107,2108,2109,2118,2120,2122,2124,2126,2128,2129,2130,2131,2133,2135,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2148,2151,2152,2153,2154,2156,2158,2159,2161,2162,2164,2166,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2179,2181,2182,2183,2184,2186,2187,2188,2189,2190,2192,2194,2196,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2218,2293,2296,2299,2302,2316,2333,2375,2378,2407,2434,2443,2507,2875,2896,2934,3072,3196,3220,3226,3255,3276,3400,3428,3434,3578,3604,3671,3746,3846,3866,3921,3933,3959", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1274,1940,1985,3032,3073,3128,3263,3327,3397,3458,3533,3609,3686,3924,4009,4091,4167,4344,4421,4499,4605,4711,4790,5119,5176,6036,6110,6185,6250,6316,6376,6437,6509,6626,6693,6761,6820,6879,6938,6997,7056,7110,7164,7217,7271,7325,7379,7723,7797,7876,7949,8023,8094,8166,8238,8311,8368,8426,8499,8573,8647,8722,8794,8867,8937,9008,9068,9129,9198,9267,9337,9411,9487,9551,9628,9704,9781,9846,9915,9992,10067,10136,10204,10281,10347,10408,10505,10570,10639,10738,10809,10868,10926,10983,11042,11106,11177,11249,11321,11393,11465,11532,11600,11668,11727,11790,11854,11944,12035,12095,12161,12228,12294,12364,12428,12481,12548,12609,12676,12789,12847,12910,12975,13040,13115,13188,13260,13304,13351,13397,13446,13507,13568,13629,13691,13755,13819,13883,13948,14011,14071,14132,14198,14257,14317,14379,14450,14510,15209,15295,15598,15688,15775,15863,15945,16028,16118,18149,18201,18259,18304,18370,18434,18491,18548,20725,20782,20830,20879,21134,21504,21551,21809,22980,23283,23347,23409,23469,23790,23864,23934,24012,24066,24136,24221,24269,24315,24376,24439,24505,24569,24640,24703,24768,24832,24893,24954,25006,25079,25153,25222,25297,25371,25445,25586,30526,30887,30965,31355,31443,31539,31702,32284,32373,32620,32901,33153,33438,33831,34308,34530,34752,35028,35255,35485,35715,35945,36175,36402,36821,37047,37472,37702,38130,38349,38632,38840,38971,39198,39624,39849,40276,40497,40922,41042,41318,41619,41943,42234,42548,42685,42816,42921,43163,43330,43534,43742,44013,44125,44237,44342,44459,44673,44819,44959,45045,45393,45481,45727,46145,46394,46476,46574,47231,47331,47583,48007,48262,48356,48445,48682,50706,50948,51050,51303,53459,64140,65656,76351,77879,79636,80262,80682,81943,83208,83464,83700,84247,84741,85346,85544,86124,87492,87867,87985,88523,88680,88876,89149,89405,89575,89716,89780,90145,90512,91188,91452,91790,92143,92237,92423,92729,92991,93116,93243,93482,93693,93812,94005,94182,94637,94818,94940,95199,95312,95499,95601,95708,95837,96112,96620,97116,97993,98287,98857,99006,99738,99910,99994,100330,100422,102564,107795,113166,113228,113806,114390,122337,122450,122679,122839,122991,123162,123328,123497,123664,123827,124070,124240,124413,124584,124858,125057,125262,125592,125676,125772,125868,125966,126066,126168,126270,126372,126474,126576,126676,126772,126884,127013,127136,127267,127398,127496,127610,127704,127844,127978,128074,128186,128286,128402,128498,128610,128710,128850,128986,129150,129280,129438,129588,129729,129873,130008,130120,130270,130398,130526,130662,130794,130924,131054,131166,132446,132592,132736,132874,132940,133030,133106,133210,133300,133402,133510,133618,133718,133798,133890,133988,134098,134150,134228,134334,134426,134530,134640,134762,134925,135082,135162,135262,135352,135462,135552,135793,135887,135993,136537,136637,136749,136863,136979,137095,137189,137303,137415,137517,137637,137759,137841,137945,138065,138191,138289,138383,138471,138583,138699,138821,138933,139108,139224,139310,139402,139514,139638,139705,139831,139899,140027,140171,140299,140368,140463,140578,140691,140790,140899,141010,141121,141222,141327,141427,141557,141648,141771,141865,141977,142063,142167,142263,142351,142469,142573,142677,142803,142891,142999,143099,143189,143299,143383,143485,143569,143623,143687,143793,143879,143989,144073,144477,147093,147211,147326,147406,147767,148353,149757,149835,151179,152540,152928,155771,166009,166748,168419,175232,179533,180284,180546,181393,181772,186050,186904,187133,191741,192751,194703,197274,201398,202142,204273,204613,205924", "endLines": "36,59,60,91,92,93,95,96,97,98,99,100,101,104,105,106,107,110,111,112,113,114,115,120,121,132,133,134,135,136,137,138,139,141,142,143,144,145,146,147,148,149,150,151,152,153,154,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,269,270,275,276,277,278,279,280,281,313,314,315,316,317,318,319,320,356,357,358,359,364,372,373,378,400,406,407,408,409,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,495,500,501,508,509,510,521,522,526,530,534,539,545,552,556,560,565,569,573,577,581,585,589,595,599,605,609,615,619,624,628,631,635,641,645,651,655,661,664,668,672,676,680,684,685,686,687,690,693,696,699,703,704,705,706,707,710,712,714,716,721,722,726,732,736,737,739,751,752,756,762,766,767,768,772,799,803,804,808,836,1008,1034,1205,1231,1262,1270,1276,1292,1314,1319,1324,1334,1343,1352,1356,1363,1382,1389,1390,1399,1402,1405,1409,1413,1417,1420,1421,1426,1431,1441,1446,1453,1459,1460,1463,1467,1472,1474,1476,1479,1482,1484,1488,1491,1498,1501,1504,1508,1510,1514,1516,1518,1520,1524,1532,1540,1552,1558,1567,1570,1581,1584,1585,1590,1591,1596,1692,1762,1763,1773,1782,1783,1936,1940,1943,1946,1949,1952,1955,1958,1961,1965,1968,1971,1974,1978,1981,1985,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2011,2013,2014,2015,2016,2017,2018,2019,2020,2022,2023,2025,2026,2028,2030,2031,2033,2034,2035,2036,2037,2038,2040,2041,2042,2043,2044,2045,2063,2065,2067,2068,2069,2070,2071,2072,2073,2074,2075,2076,2077,2078,2079,2081,2082,2083,2084,2085,2086,2087,2089,2093,2097,2098,2099,2100,2101,2102,2106,2107,2108,2109,2119,2121,2123,2125,2127,2128,2129,2130,2132,2134,2136,2137,2138,2139,2140,2141,2142,2143,2144,2145,2146,2147,2150,2151,2152,2153,2155,2157,2158,2160,2161,2163,2165,2167,2168,2169,2170,2171,2172,2173,2174,2175,2176,2177,2178,2180,2181,2182,2183,2185,2186,2187,2188,2189,2191,2193,2195,2197,2198,2199,2200,2201,2202,2203,2204,2205,2206,2207,2208,2209,2210,2211,2212,2292,2295,2298,2301,2315,2321,2342,2377,2406,2433,2442,2506,2869,2878,2923,2961,3089,3219,3225,3231,3275,3399,3419,3433,3437,3583,3638,3682,3811,3865,3920,3932,3958,3965", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "1324,1980,2029,3068,3123,3185,3322,3392,3453,3528,3604,3681,3759,4004,4086,4162,4238,4416,4494,4600,4706,4785,4865,5171,5229,6105,6180,6245,6311,6371,6432,6504,6577,6688,6756,6815,6874,6933,6992,7051,7105,7159,7212,7266,7320,7374,7428,7792,7871,7944,8018,8089,8161,8233,8306,8363,8421,8494,8568,8642,8717,8789,8862,8932,9003,9063,9124,9193,9262,9332,9406,9482,9546,9623,9699,9776,9841,9910,9987,10062,10131,10199,10276,10342,10403,10500,10565,10634,10733,10804,10863,10921,10978,11037,11101,11172,11244,11316,11388,11460,11527,11595,11663,11722,11785,11849,11939,12030,12090,12156,12223,12289,12359,12423,12476,12543,12604,12671,12784,12842,12905,12970,13035,13110,13183,13255,13299,13346,13392,13441,13502,13563,13624,13686,13750,13814,13878,13943,14006,14066,14127,14193,14252,14312,14374,14445,14505,14573,15290,15377,15683,15770,15858,15940,16023,16113,16204,18196,18254,18299,18365,18429,18486,18543,18597,20777,20825,20874,20925,21163,21546,21595,21850,23007,23342,23404,23464,23521,23859,23929,24007,24061,24131,24216,24264,24310,24371,24434,24500,24564,24635,24698,24763,24827,24888,24949,25001,25074,25148,25217,25292,25366,25440,25581,25651,30574,30960,31050,31438,31534,31624,32279,32368,32615,32896,33148,33433,33826,34303,34525,34747,35023,35250,35480,35710,35940,36170,36397,36816,37042,37467,37697,38125,38344,38627,38835,38966,39193,39619,39844,40271,40492,40917,41037,41313,41614,41938,42229,42543,42680,42811,42916,43158,43325,43529,43737,44008,44120,44232,44337,44454,44668,44814,44954,45040,45388,45476,45722,46140,46389,46471,46569,47226,47326,47578,48002,48257,48351,48440,48677,50701,50943,51045,51298,53454,64135,65651,76346,77874,79631,80257,80677,81938,83203,83459,83695,84242,84736,85341,85539,86119,87487,87862,87980,88518,88675,88871,89144,89400,89570,89711,89775,90140,90507,91183,91447,91785,92138,92232,92418,92724,92986,93111,93238,93477,93688,93807,94000,94177,94632,94813,94935,95194,95307,95494,95596,95703,95832,96107,96615,97111,97988,98282,98852,99001,99733,99905,99989,100325,100417,100695,107790,113161,113223,113801,114385,114476,122445,122674,122834,122986,123157,123323,123492,123659,123822,124065,124235,124408,124579,124853,125052,125257,125587,125671,125767,125863,125961,126061,126163,126265,126367,126469,126571,126671,126767,126879,127008,127131,127262,127393,127491,127605,127699,127839,127973,128069,128181,128281,128397,128493,128605,128705,128845,128981,129145,129275,129433,129583,129724,129868,130003,130115,130265,130393,130521,130657,130789,130919,131049,131161,131301,132587,132731,132869,132935,133025,133101,133205,133295,133397,133505,133613,133713,133793,133885,133983,134093,134145,134223,134329,134421,134525,134635,134757,134920,135077,135157,135257,135347,135457,135547,135788,135882,135988,136080,136632,136744,136858,136974,137090,137184,137298,137410,137512,137632,137754,137836,137940,138060,138186,138284,138378,138466,138578,138694,138816,138928,139103,139219,139305,139397,139509,139633,139700,139826,139894,140022,140166,140294,140363,140458,140573,140686,140785,140894,141005,141116,141217,141322,141422,141552,141643,141766,141860,141972,142058,142162,142258,142346,142464,142568,142672,142798,142886,142994,143094,143184,143294,143378,143480,143564,143618,143682,143788,143874,143984,144068,144188,147088,147206,147321,147401,147762,147995,148865,149830,151174,152535,152923,155766,165819,166139,168113,169771,175799,180279,180541,180741,181767,186045,186651,187128,187279,191951,193829,195010,200295,202137,204268,204608,205919,206122"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/66aa7f682cf61ffe3ee75db6ee238d77/transformed/core-1.13.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "61,102,103,122,123,155,156,262,263,264,265,266,267,268,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,366,367,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,413,447,448,449,450,451,452,453,496,2046,2047,2052,2055,2060,2213,2214,2879,2924,3094,3127,3157,3190", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2034,3764,3836,5234,5299,7433,7502,14721,14791,14859,14931,15001,15062,15136,16526,16587,16648,16710,16774,16836,16897,16965,17065,17125,17191,17264,17333,17390,17442,18602,18674,18750,18815,18874,18933,18993,19053,19113,19173,19233,19293,19353,19413,19473,19533,19592,19652,19712,19772,19832,19892,19952,20012,20072,20132,20192,20251,20311,20371,20430,20489,20548,20607,20666,21234,21269,21855,21910,21973,22028,22086,22144,22205,22268,22325,22376,22426,22487,22544,22610,22644,22679,23720,25990,26057,26129,26198,26267,26341,26413,30579,131306,131423,131690,131983,132250,144193,144265,166144,168118,175953,177684,178684,179366", "endLines": "61,102,103,122,123,155,156,262,263,264,265,266,267,268,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,366,367,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,413,447,448,449,450,451,452,453,496,2046,2050,2052,2058,2060,2213,2214,2884,2933,3126,3147,3189,3195", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "2089,3831,3919,5294,5360,7497,7560,14786,14854,14926,14996,15057,15131,15204,16582,16643,16705,16769,16831,16892,16960,17060,17120,17186,17259,17328,17385,17437,17499,18669,18745,18810,18869,18928,18988,19048,19108,19168,19228,19288,19348,19408,19468,19528,19587,19647,19707,19767,19827,19887,19947,20007,20067,20127,20187,20246,20306,20366,20425,20484,20543,20602,20661,20720,21264,21299,21905,21968,22023,22081,22139,22200,22263,22320,22371,22421,22482,22539,22605,22639,22674,22709,23785,26052,26124,26193,26262,26336,26408,26496,30645,131418,131619,131795,132179,132374,144260,144327,166342,168414,177679,178360,179361,179528"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/32150ec382526e3003bd883391653303/transformed/swiperefreshlayout-1.1.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "24", "endOffsets": "287"}, "to": {"startLines": "3742", "startColumns": "4", "startOffsets": "197103", "endLines": "3745", "endColumns": "24", "endOffsets": "197269"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7f734b899c9b5bcf473e5c8a79b68b93/transformed/jetified-savedstate-1.2.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "403", "startColumns": "4", "startOffsets": "23115", "endColumns": "53", "endOffsets": "23164"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/84addddb59162e1cea52976d5f2c6cc1/transformed/lifecycle-viewmodel-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "404", "startColumns": "4", "startOffsets": "23169", "endColumns": "49", "endOffsets": "23214"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/861e783eb94c5cf895f7fc30dea4170d/transformed/media-1.0.0/res/values/values.xml", "from": {"startLines": "2,5,6,7,8,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,288,341,394,447,560,626,748,809,875", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "139,336,389,442,495,621,743,804,870,937"}, "to": {"startLines": "157,309,310,311,312,2051,2053,2054,2059,2061", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "7565,17937,17990,18043,18096,131624,131800,131922,132184,132379", "endColumns": "88,52,52,52,52,65,121,60,65,66", "endOffsets": "7649,17985,18038,18091,18144,131685,131917,131978,132245,132441"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/79275990ee9dddfd68bc7c9d7157e0cd/transformed/recyclerview-1.0.0/res/values/values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "271,272,273,283,284,285,365,3584", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "15382,15441,15489,16255,16330,16406,21168,191956", "endLines": "271,272,273,283,284,285,365,3603", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "15436,15484,15540,16325,16401,16473,21229,192746"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/7b33c4ac072486c90a47d13cee761d9b/transformed/jetified-play-services-basement-18.5.0/res/values/values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "410,462", "startColumns": "4,4", "startOffsets": "23526,27541", "endColumns": "67,166", "endOffsets": "23589,27703"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e1f6d2e0b1aa38467964f5b59b4f29f9/transformed/jetified-play-services-base-18.5.0/res/values/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "124,125,126,127,128,129,130,131,454,455,456,457,458,459,460,461,463,464,465,466,467,468,469,470,471,3242,3652", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5365,5455,5535,5625,5715,5795,5876,5956,26501,26606,26787,26912,27019,27199,27322,27438,27708,27896,28001,28182,28307,28482,28630,28693,28755,181078,194286", "endLines": "124,125,126,127,128,129,130,131,454,455,456,457,458,459,460,461,463,464,465,466,467,468,469,470,471,3254,3670", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "5450,5530,5620,5710,5790,5871,5951,6031,26601,26782,26907,27014,27194,27317,27433,27536,27891,27996,28177,28302,28477,28625,28688,28750,28829,181388,194698"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/cceca150324ee75eadce8003b387b1fb/transformed/biometric-1.1.0/res/values/values.xml", "from": {"startLines": "2,6,8,11,15,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,194,277,388,523,1701,1757,1810,1886,1946,2035,2134,2242,2339,2427,2527,2597,2694,2804", "endLines": "5,7,10,14,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "189,272,383,518,1696,1752,1805,1881,1941,2030,2129,2237,2334,2422,2522,2592,2689,2799,2888"}, "to": {"startLines": "2,6,8,11,15,108,274,472,475,480,481,482,483,484,485,486,487,488,489", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,244,327,438,573,4243,15545,28834,29016,29348,29437,29536,29644,29741,29829,29929,29999,30096,30206", "endLines": "5,7,10,14,33,108,274,472,475,480,481,482,483,484,485,486,487,488,489", "endColumns": "19,19,19,19,19,55,52,75,59,88,98,107,96,87,99,69,96,109,88", "endOffsets": "239,322,433,568,1149,4294,15593,28905,29071,29432,29531,29639,29736,29824,29924,29994,30091,30201,30290"}}, {"source": "/Users/<USER>/AndroidStudioProjects/sbar_pos/android/app/src/main/res/values/styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "173,818", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "476,982"}, "to": {"startLines": "1617,1621", "startColumns": "4,4", "startOffsets": "102214,102395", "endLines": "1620,1623", "endColumns": "12,12", "endOffsets": "102390,102559"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/28f988f0d4c2cc22199e4c3cefdd595e/transformed/coordinatorlayout-1.0.0/res/values/values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "35,2215,2962,2968", "startColumns": "4,4,4,4", "startOffsets": "1213,144332,169776,169987", "endLines": "35,2217,2967,3051", "endColumns": "60,12,24,24", "endOffsets": "1269,144472,169982,174498"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/85879f220671a879b538e8ef16ed1744/transformed/jetified-startup-runtime-1.1.1/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "446", "startColumns": "4", "startOffsets": "25907", "endColumns": "82", "endOffsets": "25985"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/aa55b2079cbc673a6a445c1850daa153/transformed/lifecycle-runtime-2.7.0/res/values/values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "401", "startColumns": "4", "startOffsets": "23012", "endColumns": "42", "endOffsets": "23050"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/1c8746a36ac065afed39d95b2852a559/transformed/fragment-1.7.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "361,377,405,3148,3153", "startColumns": "4,4,4,4,4", "startOffsets": "20990,21744,23219,178365,178535", "endLines": "361,377,405,3152,3156", "endColumns": "56,64,63,24,24", "endOffsets": "21042,21804,23278,178530,178679"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/197f12b192a3f06912c946d4cbd2dd7d/transformed/jetified-window-1.2.0/res/values/values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "34,37,43,51,62,74,80,86,87,88,89,90,360,2322,2328,3683,3691,3706", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1154,1329,1502,1721,2094,2408,2596,2783,2836,2896,2948,2993,20930,148000,148195,195015,195297,195911", "endLines": "34,42,50,58,73,79,85,86,87,88,89,90,360,2327,2332,3690,3705,3721", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "1208,1497,1716,1935,2403,2591,2778,2831,2891,2943,2988,3027,20985,148190,148348,195292,195906,196560"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/b83b8b00b8346c9e7414a1f1298f055d/transformed/preference-1.2.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "94,158,302,303,304,305,306,307,308,369,370,371,411,412,473,476,491,492,497,498,499,1597,1784,1787,1793,1799,1802,1808,1812,1815,1822,1828,1831,1837,1842,1847,1854,1856,1862,1868,1876,1881,1888,1893,1899,1903,1910,1914,1920,1926,1929,1933,1934,2870,2885,3052,3090,3232,3420,3438,3502,3512,3522,3529,3535,3639,3812,3829", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3190,7654,17504,17568,17623,17691,17758,17823,17880,21347,21395,21443,23594,23657,28910,29076,30342,30386,30650,30789,30839,100700,114481,114586,114831,115169,115315,115655,115867,116030,116437,116775,116898,117237,117476,117733,118104,118164,118502,118788,119237,119529,119917,120222,120566,120811,121141,121348,121616,121889,122033,122234,122281,165824,166347,174503,175804,180746,186656,187284,189209,189491,189796,190058,190318,193834,200300,200830", "endLines": "94,158,302,303,304,305,306,307,308,369,370,371,411,412,473,476,491,494,497,498,499,1613,1786,1792,1798,1801,1807,1811,1814,1821,1827,1830,1836,1841,1846,1853,1855,1861,1867,1875,1880,1887,1892,1898,1902,1909,1913,1919,1925,1928,1932,1933,1934,2874,2895,3071,3093,3241,3427,3501,3511,3521,3528,3534,3577,3651,3828,3845", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "3258,7718,17563,17618,17686,17753,17818,17875,17932,21390,21438,21499,23652,23715,28943,29128,30381,30521,30784,30834,30882,102133,114581,114826,115164,115310,115650,115862,116025,116432,116770,116893,117232,117471,117728,118099,118159,118497,118783,119232,119524,119912,120217,120561,120806,121136,121343,121611,121884,122028,122229,122276,122332,166004,166743,175227,175948,181073,186899,189204,189486,189791,190053,190313,191736,194281,200825,201393"}}, {"source": "/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml", "from": {"startLines": "2,3,4,5,6", "startColumns": "4,4,4,4,4", "startOffsets": "55,100,144,190,238", "endLines": "2,3,4,5,11", "endColumns": "44,43,45,47,10", "endOffsets": "95,139,185,233,533"}, "to": {"startLines": "109,140,282,286,502", "startColumns": "4,4,4,4,4", "startOffsets": "4299,6582,16209,16478,31055", "endLines": "109,140,282,286,507", "endColumns": "44,43,45,47,10", "endOffsets": "4339,6621,16250,16521,31350"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0c69679757972620720ec039d7103818/transformed/browser-1.8.0/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "116,117,118,119,260,261,474,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "4870,4928,4994,5057,14578,14649,28948,29133,29200,29279", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "4923,4989,5052,5114,14644,14716,29011,29195,29274,29343"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/93eeca70efd8419049cd49df8af72af1/transformed/jetified-activity-1.9.3/res/values/values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "374,402", "startColumns": "4,4", "startOffsets": "21600,23055", "endColumns": "41,59", "endOffsets": "21637,23110"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/0f448994e490bf59f20aa19226509e50/transformed/jetified-appcompat-resources-1.6.1/res/values/values.xml", "from": {"startLines": "2,18,24,34,50", "startColumns": "4,4,4,4,4", "startOffsets": "55,480,658,942,1353", "endLines": "17,23,33,49,53", "endColumns": "24,24,24,24,24", "endOffsets": "475,653,937,1348,1475"}, "to": {"startLines": "2343,2359,2365,3722,3738", "startColumns": "4,4,4,4,4", "startOffsets": "148870,149295,149473,196565,196976", "endLines": "2358,2364,2374,3737,3741", "endColumns": "24,24,24,24,24", "endOffsets": "149290,149468,149752,196971,197098"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/f87704cc6ac259b753f491455f413615/transformed/transition-1.4.1/res/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "362,363,368,375,376,395,396,397,398,399", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "21047,21087,21304,21642,21697,22714,22768,22820,22869,22930", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "21082,21129,21342,21692,21739,22763,22815,22864,22925,22975"}}, {"source": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/android/build/intermediates/packaged_res/debug/packageDebugResources/values/values.xml", "from": {"startLines": "2,3,4,5,6,7,8,11,14", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,102,153,210,259,306,353,426,502", "endLines": "2,3,4,5,6,7,10,13,21", "endColumns": "46,50,56,48,46,46,12,12,12", "endOffsets": "97,148,205,254,301,348,421,497,949"}, "to": {"startLines": "441,442,443,444,445,490,511,1614,2110", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "25656,25703,25754,25811,25860,30295,31629,102138,136085", "endLines": "441,442,443,444,445,490,513,1616,2117", "endColumns": "46,50,56,48,46,46,12,12,12", "endOffsets": "25698,25749,25806,25855,25902,30337,31697,102209,136532"}}]}]}