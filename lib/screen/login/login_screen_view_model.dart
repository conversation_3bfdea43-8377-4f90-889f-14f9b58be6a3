import 'package:flutter/cupertino.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:http/http.dart' as http;
import 'package:sbar_pos/api/all_api.dart';
import 'package:sbar_pos/screen/start/my_app.dart';

import '../../hive/hive_provider/hive_user_provider.dart';
import '../../model/login/staff_login_list_model.dart';
import '../../util/MyRoutes.dart';
import '../main/main_screen.dart';

class LoginScreenViewModel with ChangeNotifier {
  TextEditingController accountController = TextEditingController();
  TextEditingController pwdController = TextEditingController();
  TextEditingController shopNameController = TextEditingController(); //商店名稱
  TextEditingController shopCodeController = TextEditingController(); //商店代號

  AnimationController? animationAccountController;
  AnimationController? animationPwdController;

  bool _loading = false;

  bool get loading => _loading;

  set loading(bool value) {
    _loading = value;
    notifyListeners();
  }

  ///員工登入表
  List<StaffLoginListModel> staffLoginListModel = [
    StaffLoginListModel(
      name: '王大明',
      avatar:
          'https://blog.fluv.com/wp-content/uploads/sites/2/2023/08/未命名設計-9-809x455.png',
      loginTime: '2025-07-02 14:02',
      position: '店長',
    ),
    StaffLoginListModel(
      name: 'Tom',
      avatar:
          'https://blog.fluv.com/wp-content/uploads/sites/2/2023/08/未命名設計-9-809x455.png',
      loginTime: '2025-07-02 14:02',
      position: '店員',
    ),
    StaffLoginListModel(
      name: 'Jimmy',
      avatar:
          'https://blog.fluv.com/wp-content/uploads/sites/2/2023/08/未命名設計-9-809x455.png',
      loginTime: '2025-07-01 14:02',
      position: '店員',
    ),
    StaffLoginListModel(
      name: 'Lane',
      avatar:
          'https://blog.fluv.com/wp-content/uploads/sites/2/2023/08/未命名設計-9-809x455.png',
      loginTime: '2025-06-28 13:02',
      position: '店員',
    ),
    StaffLoginListModel(
      name: 'Alvin',
      avatar:
          'https://blog.fluv.com/wp-content/uploads/sites/2/2023/08/未命名設計-9-809x455.png',
      loginTime: '2025-06-25 14:02',
      position: '店員',
    ),
    StaffLoginListModel(
      name: 'Ozzie',
      avatar:
          'https://blog.fluv.com/wp-content/uploads/sites/2/2023/08/未命名設計-9-809x455.png',
      loginTime: '2025-06-24 14:02',
      position: '店員',
    ),
  ];

  ///登入邏輯
  void loginLogic(BuildContext context, Function()? yesAction) async {
    if (loading) return;

    //帳號為空處理
    if (accountController.text.isEmpty) {
      animationAccountController?.reset();
      animationAccountController?.forward();
      return;
    }

    //密碼為空處理
    if (pwdController.text.isEmpty) {
      animationPwdController?.reset();
      animationPwdController?.forward();
      return;
    }
    loading = true;

    ///改變UserDB狀態
    final user = await HiveUserProvider.getUser();
    user.shopName = shopNameController.text;
    user.shopId = shopCodeController.text;
    await HiveUserProvider.updateUser(user);

    await AllApi().apiLogin(
      context,
      account: accountController.text,
      pwd: pwdController.text,
      groupName: user.shopName ?? shopNameController.text,
      groupCode: user.shopId ?? shopCodeController.text,
    );
    loading = false;
  }
}
