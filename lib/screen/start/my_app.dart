import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:provider/provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../App.dart';
import '../../hive/entity/user.dart';
import '../../hive/helper/boxes.dart';
import '../../hive/hive_provider/hive_user_provider.dart';
import '../../logger/my_print.dart';
import '../../provider/loading_provider.dart';
import '../../provider/main_provider.dart';
import '../../provider/shopping_car_provider.dart';
import '../../provider/version_provider.dart';
import '../../route_observer/my_route_observer.dart';
import '../../route_observer/page_info.dart';
import '../../sqlite/DatabaseHelper.dart';
import '../../sqlite/provider/product_shopping_car_record_provider.dart';
import '../../static/net_work_listener.dart';
import '../login/login_screen.dart';
import '../member/member_info/member_info_main_screen_view_model.dart';
import '../../provider/reserve_calendar_provider.dart';


final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey = GlobalKey();
final GlobalKey<NavigatorState> navigatorKey = GlobalKey();
bool isLoading = true;

void setLoadingStatus(bool isLoading) {
  navigatorKey.currentContext?.read<LoadingProvider>().isLoading = isLoading;
}

void showLoadingOverlay() => setLoadingStatus(true);

void dismissLoadingOverlay() => setLoadingStatus(false);

class MyApp extends StatefulWidget {
  final String? initialPayload;
  final Uri? initialDeepLink;

  const MyApp({super.key, this.initialPayload, this.initialDeepLink});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  StreamSubscription<Uri>? uriLinkSubscription;
  StreamSubscription<ConnectivityResult>? networkSubscription;
  Future<void>? _initialization;
  // FlutterLocalNotificationsPlugin notificationsPlugin =
  //     FlutterLocalNotificationsPlugin();

  @override
  void initState() {
    super.initState();
    _initTimeZone();
    _initialization = initialSetting();

    // 監聽網路狀態
    networkSubscription = Rx.merge([
      Connectivity().checkConnectivity().asStream(),
      Connectivity().onConnectivityChanged,
    ]).listen((result) {
      final networkListener = NetworkListener();
      networkListener.pushConnectionEvent(result);
      checkNeedShowBanner(networkListener);
    });


  }



  @override
  void dispose() {
    networkSubscription?.cancel();
    uriLinkSubscription?.cancel();
    super.dispose();
  }

  void checkNeedShowBanner(NetworkListener listener) {
    if (listener.isConnection) {
      dismissNetworkSnackBar();
    } else {
      showNetworkSnackBar();
    }
  }

  void showNetworkSnackBar() {
    scaffoldMessengerKey.currentState?.showSnackBar(
      const SnackBar(
        content: Text('當前無網路..'),
        duration: Duration(days: 1),
        backgroundColor: Colors.blueGrey,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 關閉`SnackBar`
  void dismissNetworkSnackBar() {
    scaffoldMessengerKey.currentState?.hideCurrentSnackBar();


  }

  ///初始化時區
  void _initTimeZone() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      tz.initializeTimeZones();
    });
  }

  @override
  Widget build(BuildContext context) {
    // final double maxWidth = 1920;
    // final double maxHeight = 1200;
    final child = MultiProvider(
      providers: [
        ChangeNotifierProvider(
            create: (context) => LoadingProvider(), lazy: false),
        ChangeNotifierProvider(
            create: (context) => ShoppingCarProvider(), lazy: false),
        ChangeNotifierProvider(
            create: (context) => MainProvider(), lazy: false),
        ChangeNotifierProvider(
            create: (context) => ReserveCalendarScreenProvider(), lazy: false),
        ChangeNotifierProvider(
            create: (context) => VersionProvider(), lazy: false),

      ],
      child: ScreenUtilInit(
        designSize: Size(1920, 1200),
        minTextAdapt: true,
        child: MaterialApp(
            navigatorKey: navigatorKey,
            scaffoldMessengerKey: scaffoldMessengerKey,
            scrollBehavior: const CupertinoScrollBehavior(),
            theme: ThemeData(useMaterial3: false),
            navigatorObservers: [
              MyRouteObserver(),
              // googleAnalyticsObserver,
            ],
            localizationsDelegates: const [
              // S.delegate,
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            // supportedLocales: S.delegate.supportedLocales,
            builder: (context, home) {
              final wrapped = Stack(
                children: [
                  if (home != null) home,
                  createLoadingOverlay(),
                ],
              );
              return Overlay(
                initialEntries: [
                  OverlayEntry(
                    builder: (context) => Center(
                      child: wrapped,
                    ),
                  ),
                ],
              );
            },
            debugShowCheckedModeBanner: false,
            ///TODO:登入頁
            home: _initialization == null
              ? const Center(child: CircularProgressIndicator())
              : FutureBuilder<void>(
                  future: _initialization,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      return LoginScreen();
                    } else {
                      return const Center(child: CircularProgressIndicator());
                    }
                  },
                ),
          ),
      ),
    );

    return Builder(
      builder: (context) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(textScaler: const TextScaler.linear(1)),
          child: child,
        );
      },
    );
  }

  Widget createLoadingOverlay() {
    return Consumer<LoadingProvider>(
      builder: (context, vm, loading) {
        return Positioned.fill(
          child: AnimatedSwitcher(
            duration: 200.ms,
            child: vm.isLoading
                ? ColoredBox(
                    color: Colors.black45,
                    child: Center(
                      child: Container(
                        padding: const EdgeInsets.all(24),
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius:
                              const BorderRadius.all(Radius.circular(12.0)),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.white.withOpacity(0.3),
                              spreadRadius: 2,
                              blurRadius: 3,
                            ),
                          ],
                        ),
                        child: const CircularProgressIndicator(
                            color: Colors.white),
                      ).animate().scale(
                            duration: 250.ms,
                            begin: const Offset(0.5, 2),
                            // end: const Offset(1, 1),
                            curve: Curves.easeOutBack,
                          ),
                    ),
                  )
                : null,
          ),
        );
      },
    );
  }
}

Future<T?> pushTo<T extends Object?>(
  String pageName, {
  required WidgetBuilder builder,
  bool avoidSamePage = true,
  bool? isPushReplacement,
  BuildContext? context,
}) async {
  context ??= navigatorKey.currentContext;

  if (context == null) return null;
  final currentPageName = PageInfo().pageName.value;
  myPrint('currentPageName:$currentPageName,pageName: $pageName');
  if (avoidSamePage && currentPageName == pageName) return null;

  return (isPushReplacement ?? false)
      ? navigatorKey.currentState?.pushReplacement(
          MaterialPageRoute(
            settings: RouteSettings(name: pageName),
            builder: builder,
          ),
        )
      : navigatorKey.currentState?.push(
          MaterialPageRoute(
            settings: RouteSettings(name: pageName),
            builder: builder,
          ),
        );
}

Future<T?> pushAndRemoveUntil<T extends Object?>(
  String pageName, {
  required WidgetBuilder builder,
  bool avoidSamePage = true,
  BuildContext? context,
}) async {
  context ??= navigatorKey.currentContext;

  if (context == null) return null;
  final currentPageName = PageInfo().pageName.value;
  myPrint('currentPageName:$currentPageName,pageName: $pageName');
  // if (avoidSamePage && currentPageName == pageName) return null;

  return navigatorKey.currentState?.pushAndRemoveUntil(
    MaterialPageRoute(
      settings: RouteSettings(name: pageName),
      builder: builder,
    ),
    (route) => false,
  );
}

String? initialPayload;

Future<void> initialSetting() async {

  await hiveInit();
  await sqliteInit();
  await initShoppingCar();

}



///init Hive
Future<void> hiveInit() async {
  await Hive.initFlutter();
  Hive.registerAdapter(HiveUserAdapter());
  await Hive.openBox<HiveUser>(Boxes.userBoxName);
  await HiveUserProvider.initDB();
}

///init SqLite
Future<void> sqliteInit() async {
  await DatabaseHelper().createTable();
}

///init shoppingCar
Future<void> initShoppingCar() async {

  // 清空購物車
  final shoppingCarProvider = ProductShoppingCarRecordProvider();
  await shoppingCarProvider.createTableIfNotExists();
  await shoppingCarProvider.clearAll();
}





